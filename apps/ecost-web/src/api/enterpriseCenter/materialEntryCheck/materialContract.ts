import { ecostServiceRequestClient } from '#/api/request';

export const AuditStatus = {
  PENDING: 'PENDING',
  AUDITING: 'AUDITING',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED',
} as const;
export type AuditStatusEnum = (typeof AuditStatus)[keyof typeof AuditStatus];

export const MaterialReceiptStatus = {
  UN_RECEIVED: 'UN_RECEIVED',
  RECEIVED: 'RECEIVED',
  PARTIAL_RECEIVED: 'PARTIAL_RECEIVED',
} as const;
export type MaterialReceiptStatusEnum =
  (typeof MaterialReceiptStatus)[keyof typeof MaterialReceiptStatus];

export const MaterialType = {
  CONSUME_MATERIAL: 'CONSUME_MATERIAL',
  CONCRETE: 'CONCRETE',
  TURNOVERME_MATERIAL: 'TURNOVERME_MATERIAL',
  FIXEDASSETSL_CONSUMABLES: 'FIXEDASSETSL_CONSUMABLES',
} as const;
export type MaterialTypeEnmu = (typeof MaterialType)[keyof typeof MaterialType];

export const PurchaseType = {
  SELF_PURCHASE: 'SELF_PURCHASE',
  CENTRALIZED_PURCHASE: 'CENTRALIZED_PURCHASE',
  PARTY_A_DIRECTED: 'PARTY_A_DIRECTED',
  PARTY_A_SUPPLIED: 'PARTY_A_SUPPLIED',
  TRANSFER_IN: 'TRANSFER_IN',
} as const;
export type PurchaseTypeEnum = (typeof PurchaseType)[keyof typeof PurchaseType];

export const SubmitStatus = {
  PENDING: 'PENDING',
  SUBMITTED: 'SUBMITTED',
} as const;
export type SubmitStatusEnum = (typeof SubmitStatus)[keyof typeof SubmitStatus];

export enum MaterialSearchType {
  CONTRACT = 'CONTRACT',
  MATERIAL_DICT = 'MATERIAL_DICT',
}
/**
 * 材料进场验收单 -- 获取时间筛选列表
 *
 */
export function getTimeList() {
  return ecostServiceRequestClient.get(
    `/material-incoming-inspection/time-list`,
  );
}

/**
 * 材料进场验收单 -- 获取单据列表
 *
 */
interface getInspectionBillListType {
  year?: number;
  month?: number;
  day?: number;
}
export function getInspectionBillList(params: getInspectionBillListType) {
  return ecostServiceRequestClient.get(
    `/material-incoming-inspection/inspection-bill/list`,
    {
      params,
    },
  );
}

/**
 * 材料进场验收单 -- 新增进场验收单据
 *
 */
export function addInspectionBill() {
  return ecostServiceRequestClient.post(
    `/material-incoming-inspection/inspection-bill/_add`,
  );
}

/**
 * 材料进场验收单 -- 修改进场验收单据
 *
 */
interface editInspectionBillType {
  id: string;
  purchaseType: 'SELF_PURCHASE';
  supplierId: string;
  supplierName: string;
  contractId: string;
  contractName: string;
  submitStatus: SubmitStatusEnum;
  auditStatus: AuditStatusEnum;
  year: 0;
  month: 0;
  day: 0;
}
export function editInspectionBill(data: editInspectionBillType) {
  return ecostServiceRequestClient.post(
    `/material-incoming-inspection/inspection-bill/_edit`,
    data,
  );
}

/**
 * 材料进场验收单 -- 删除进场验收单据
 *
 */
export function delInspectionBill(id: string) {
  return ecostServiceRequestClient.delete(
    `/material-incoming-inspection/inspection-bill/${id}`,
  );
}
/**
 * 材料进场验收单 -- 获取供应商和合同列表
 *
 */

interface getSupplierAndContractlListType {
  purchaseType: PurchaseTypeEnum;
}
export function getSupplierAndContractlList(
  params: getSupplierAndContractlListType,
) {
  return ecostServiceRequestClient.get(
    `/material-incoming-inspection/supplier-and-contract/list`,
    {
      params,
    },
  );
}

/**
 * 材料进场验收单 -- 获取验收单明细列表
 *
 */

export function getInspectionDetailList(inspectionBillId: string) {
  return ecostServiceRequestClient.get(
    `/material-incoming-inspection/${inspectionBillId}/detail/list`,
  );
}

/**
 * 材料进场验收单 -- 获取可选的材料字典分类
 *
 */

interface getMaterialCategoryListType {
  inspectionBillId: string;
  materialSearchType: string;
  purchaseType: string;
}
export function getMaterialCategoryList(params: getMaterialCategoryListType) {
  return ecostServiceRequestClient.get(
    `/material-incoming-inspection/${params.inspectionBillId}/detail/material-category/list`,
    {
      params,
    },
  );
}

/**
 * 材料进场验收单 -- 获取可选择的材料明细
 *
 */
interface getMaterialDetailListType {
  inspectionBillId: string;
  materialSearchType: string;
  purchaseType: string;
}
export function getMaterialDetailList(params: getMaterialDetailListType) {
  return ecostServiceRequestClient.get(
    `/material-incoming-inspection/${params.inspectionBillId}/detail/material-category/list`,
    {
      params,
    },
  );
}

/**
 * 材料进场验收单 -- 新增验收单明细
 *
 */
interface addInspectionDetailType {
  materialId: string;
  materialName: string;
  materialSpec: string;
  unit: string;
}
export function addInspectionDetail(
  inspectionBillId: string,
  data: addInspectionDetailType,
) {
  return ecostServiceRequestClient.post(
    `/material-incoming-inspection/${inspectionBillId}/detail/_add`,
    data,
  );
}

/**
 * 材料进场验收单 -- 删除验收单明细
 *
 */
export function delInspectionDetail(id: string) {
  return ecostServiceRequestClient.post(
    `/material-incoming-inspection/inspection-detail/${id}`,
  );
}

/**
 * 材料进场验收单 -- 编辑验收单明细
 *
 */
interface editInspectionDetail {
  id: string;
  qualityStandard: string;
  unit: string;
  siteEntryQuantity: 0;
  actualQuantity: 0;
  appearanceDescription: string;
  orderNo: 0;
  remark: string;
}
export function editInspectionDetail(data: editInspectionDetail) {
  return ecostServiceRequestClient.post(
    `/material-incoming-inspection/inspection-detail/_edit`,
    data,
  );
}

/**
 * 材料进场验收单 -- 验收单附件上传
 *
 */
interface editInspectionDetail {
  incomingInspectionId: string;
  fileName: string;
  fileKey: string;
  fileSize: number;
  fileExt: string;
  fileContentType: string;
}
export function addInspectionBillAttachment(data: editInspectionDetail) {
  return ecostServiceRequestClient.post(
    `/material-incoming-inspection/inspection-bill/attachment`,
    data,
  );
}

/**
 * 材料进场验收单 -- 获取验收单附件列表
 *
 */

export function getAttachmentList(inspectionBillId: string) {
  return ecostServiceRequestClient.get(
    `/material-incoming-inspection/${inspectionBillId}/attachment/list`,
  );
}

/**
 * 材料进场验收单 -- 验收单附件删除
 *
 */
export function delAttachmen(id: string) {
  return ecostServiceRequestClient.delete(
    `/material-incoming-inspection/inspection-bill/attachment/${id}`,
  );
}
