<template>
  <ElDrawer
    v-bind="$attrs"
    v-model="drawerVisible"
    :modal="false"
    :destroy-on-close="true"
    :append-to-body="true"
    :size="sizeNum"
    modal-class="pointer-events-none"
    class="pointer-events-auto"
    :with-header="false"
    @close="handleClose"
    @opened="handleOpen"
  >
    <div class="header box-border w-full pl-4 pr-4">
      <div class="flex h-[60px] w-full items-center justify-between">
        <div class="header-left flex items-center justify-between"></div>
        <div class="header-right flex">
          <div class="btn-group ml-4 pr-4">
            <ElButton type="primary" size="default" @click="insureSubmit">
              {{
                itemInfo.submitStatus === SubmitStatus.PENDING
                  ? '提交'
                  : '取消提交'
              }}
            </ElButton>
            <ElButton
              type="primary"
              size="default"
              @click="insureSubmit"
              :disabled="true"
            >
              发起审核
            </ElButton>
            <ElButton type="default" size="default" @click="downloadContract">
              导出单据
            </ElButton>
          </div>
          <div class="flex">
            <IconifyIcon
              @click="prevBtn"
              class="icon-box mr-4 text-3xl outline-none hover:cursor-pointer hover:text-[#006be6]"
              icon="majesticons:chevron-left-line"
            />
            <IconifyIcon
              @click="nextBtn"
              class="icon-box mr-5 text-3xl outline-none hover:cursor-pointer hover:text-[#006be6]"
              icon="majesticons:chevron-right-line"
            />
          </div>
          <div>
            <ElTooltip
              :content="isFullScreen ? '收起' : '全屏'"
              placement="bottom"
            >
              <IconifyIcon
                @click="isFullScreen = !isFullScreen"
                class="icon-box mr-5 text-3xl outline-none hover:cursor-pointer hover:text-[#006be6]"
                :icon="
                  isFullScreen
                    ? 'majesticons:arrows-collapse-full'
                    : 'majesticons:arrows-expand-full-line'
                "
              />
            </ElTooltip>
          </div>
          <div>
            <IconifyIcon
              @click="closeClick"
              class="icon-box mr-5 text-3xl outline-none hover:cursor-pointer hover:text-[#006be6]"
              icon="ep:close"
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="content relative h-[calc(100%-60px)] overflow-hidden p-[16px] pb-0"
    ></div>
  </ElDrawer>
</template>

<script lang="ts" setup>
import { computed, onMounted, provide, ref, watch } from 'vue';

import { IconifyIcon } from '@vben/icons';

import { ElButton, ElDrawer, ElMessage, ElTooltip } from 'element-plus';

import { getFileCloudUrl } from '#/api/couldApi';
import { changeSubmitStatus } from '#/api/enterpriseCenter/materialManagement/materialContract';
import { downloadUrlFile } from '#/utils';

defineOptions({
  name: 'ContractTemplateEditor',
});
const props = withDefaults(
  defineProps<{
    editable?: boolean;
    infoData: any;
    visible: boolean;
  }>(),
  {
    editable: true,
    visible: false,
    infoData: {
      parentId: null,
      contractId: '',
      contractTemplateType: '',
      submitStatus: '',
    },
  },
);
const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'refresh'): void;
  (e: 'move', payload: any): void;
}>();

const drawerVisible = ref(props.visible); // 弹窗是否展示
const isFullScreen = ref(false); // 是否全屏
const sizeNum = computed(() => {
  return isFullScreen.value ? '100%' : '74%';
});

// 合同信息数据
const itemInfo = ref(props.infoData);
watch(
  () => props.infoData,
  (nval) => {
    itemInfo.value = nval;
  },
);

const prevBtn = () => {
  emit('move', -1);
};
const nextBtn = () => {
  emit('move', 1);
};

const editableValue = ref(props.editable);
watch(
  () => props.editable,
  (nval) => {
    editableValue.value = nval;
  },
);
provide('editable', editableValue);

// 是否展示弹窗
watch(
  () => props.visible,
  (nval) => {
    drawerVisible.value = nval;
  },
);
// 下载范本
async function downloadContract() {
  const fileKeys = [itemForm.value.fileKey];
  const urlData = await getFileCloudUrl(fileKeys);

  const url = urlData[itemForm.value.fileKey];

  await downloadUrlFile(url, '');
}

const SubmitStatus = {
  PENDING: 'PENDING', // 未提交
  SUBMITTED: 'SUBMITTED', // 已提交
} as const;

// 确认提交
async function insureSubmit() {
  const id = itemInfo.value.contractId;
  const status =
    itemInfo.value.submitStatus === SubmitStatus.PENDING
      ? SubmitStatus.SUBMITTED
      : SubmitStatus.PENDING;
  const text =
    itemInfo.value.submitStatus === SubmitStatus.PENDING ? '提交' : '取消';
  const params = {
    submitStatus: status,
  };
  const res = await changeSubmitStatus(id, params);
  if (res) {
    itemInfo.value.submitStatus = status;
    if (status === SubmitStatus.SUBMITTED) {
      editableValue.value = true;
    }

    ElMessage.success(`${text}成功`);
    emit('update:visible', false);
    emit('refresh');
  }
}

// 弹窗打开回调
function handleOpen() {}

// 弹窗关闭的回调
function handleClose() {
  emit('update:visible', false);
}

async function closeClick() {
  drawerVisible.value = false;
}

async function init() {}

onMounted(() => {
  init();
});
</script>

<style lang="scss" scoped>
.el-drawer__header {
  padding: 0 16px;
  margin-bottom: 0;
}

.customer-drawer {
  width: 100% !important;
}

.customer-modal {
  // width: 80% !important;/
  min-width: 1200px;
  margin-left: auto;
}
</style>

<style>
.pointer-events-none {
  z-index: 210 !important;
}
.el-drawer__body {
  padding: 0;
}
</style>
