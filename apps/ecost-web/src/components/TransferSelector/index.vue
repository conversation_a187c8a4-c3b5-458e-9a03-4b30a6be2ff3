<template>
  <div class="flex h-[440px] items-center justify-between">
    <!-- 左部确认区 -->
    <div class="h-full w-[66%]">
      <Choices
        :choice-class-data="choiceClass"
        :choice-detail-data="choiceDetail"
        :selection-data="selections"
        @select="choicesSelect"
        @change="choicesChange"
        @class-search="classSearch"
        @deatil-search="deatilSearch"
      />
    </div>
    <!-- 中部按钮区 -->
    <div class="flex h-full items-center justify-center">
      <IconifyIcon class="text-2xl text-gray-500" icon="bi:chevron-right" />
      <IconifyIcon
        class="ml-[-16px] text-2xl text-gray-500"
        icon="bi:chevron-right"
      />
    </div>
    <!-- 右部确认区 -->
    <div class="h-full w-[30%]">
      <Selections :selection-data="selections" @change="selectionChange" />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, watch } from 'vue';

import { IconifyIcon } from '@vben/icons';

import Choices from './components/LeftChoices.vue';
import Selections from './components/RightSelections.vue';

const props = withDefaults(
  defineProps<{
    choiceClassData: any[];
    choiceDetailData: any[];
    selectionData: any[];
  }>(),
  {
    selectionData: () => [],
    choiceClassData: () => [],
    choiceDetailData: () => [],
  },
);

const emit = defineEmits<{
  (e: 'update:selectionData', selectionData: any[]): void;
  (e: 'select', data: any): void;
  (e: 'classSearch', text: any): void;
  (e: 'deatilSearch', text: any, row: any): void;
}>();

// 确认区
const selections = ref(props.selectionData);
watch(
  () => props.selectionData,
  (nval) => {
    selections.value = nval;
  },
  { deep: true, immediate: true },
);
// 选择区:分类
const choiceClass = ref<any[]>([]);
const currentClassItem = ref();
watch(
  () => props.choiceClassData,
  (nval) => {
    choiceClass.value = nval;
  },
  { deep: true, immediate: true },
);
// 选择区:明细
const choiceDetail = ref<any[]>([]);
watch(
  () => props.choiceDetailData,
  (nval) => {
    choiceDetail.value = nval;
  },
  { deep: true, immediate: true },
);

// 选中分类
async function choicesSelect(row: any) {
  currentClassItem.value = row;
  emit('select', row);
}
// 分类筛选
async function classSearch(text: any) {
  emit('classSearch', text);
}

// 明细筛选
async function deatilSearch(text: any) {
  emit('deatilSearch', text, currentClassItem.value);
}

// 选择器改变
async function choicesChange(row: any) {
  const newSelections = [...selections.value, row];
  selections.value = newSelections;
  emit('update:selectionData', newSelections);
}
// 已选择的数据改变
async function selectionChange(row: any) {
  const idx = selections.value.findIndex((v) => v.id === row.id);
  if (idx !== -1) {
    const newSelections = selections.value.filter((item) => item.id !== row.id);
    selections.value = newSelections;
    emit('update:selectionData', newSelections);
  }
}
</script>
<style scoped lang="scss"></style>
